# -*- coding: UTF-8 -*-
"""
测试所有导入的包
"""

print("开始测试导入...")

try:
    import cv2
    print("✓ cv2 导入成功")
except ImportError as e:
    print(f"✗ cv2 导入失败: {e}")

try:
    import requests
    print("✓ requests 导入成功")
except ImportError as e:
    print(f"✗ requests 导入失败: {e}")

try:
    import os
    print("✓ os 导入成功")
except ImportError as e:
    print(f"✗ os 导入失败: {e}")

try:
    from ultralyticsplus import YOLO, render_result
    print("✓ ultralyticsplus 导入成功")
except ImportError as e:
    print(f"✗ ultralyticsplus 导入失败: {e}")

try:
    import yfinance as yf
    print("✓ yfinance 导入成功")
except ImportError as e:
    print(f"✗ yfinance 导入失败: {e}")

try:
    import pandas as pd
    print("✓ pandas 导入成功")
except ImportError as e:
    print(f"✗ pandas 导入失败: {e}")

try:
    import mplfinance as mpf
    print("✓ mplfinance 导入成功")
except ImportError as e:
    print(f"✗ mplfinance 导入失败: {e}")

try:
    from PIL import Image
    print("✓ PIL 导入成功")
except ImportError as e:
    print(f"✗ PIL 导入失败: {e}")

try:
    import numpy as np
    print("✓ numpy 导入成功")
except ImportError as e:
    print(f"✗ numpy 导入失败: {e}")

try:
    import ssl
    print("✓ ssl 导入成功")
except ImportError as e:
    print(f"✗ ssl 导入失败: {e}")

try:
    import urllib3
    print("✓ urllib3 导入成功")
except ImportError as e:
    print(f"✗ urllib3 导入失败: {e}")

try:
    from requests.adapters import HTTPAdapter
    from urllib3.util.retry import Retry
    print("✓ requests 相关模块导入成功")
except ImportError as e:
    print(f"✗ requests 相关模块导入失败: {e}")

print("\n导入测试完成！")
print("如果所有包都显示成功，那么问题可能在其他地方。")
