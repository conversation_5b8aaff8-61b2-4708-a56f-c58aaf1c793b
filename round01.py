# -*- coding: UTF-8 -*-
"""
@Project : sember_fleet
@File    : round01_fixed.py
@IDE     : PyCharm
<AUTHOR> 等等 <<EMAIL>>
@Date    : 2025/7/4 15:16
"""

import cv2
import requests
import os
from ultralyticsplus import YOLO, render_result
import yfinance as yf
import pandas as pd
import mplfinance as mpf
from PIL import Image
import numpy as np
import tushare as ts
import torch

torch.serialization.add_safe_globals(['ultralytics.nn.tasks.DetectionModel'])

# def fetch_stock_data(symbol, start_date, end_date):
#     """
#     使用 yfinance 获取股票数据
#     """
#     data = yf.download(symbol, start=start_date, end=end_date)
#     return data

def fetch_stock_data(ts_code, start_date, end_date):
    """
    使用 tushare 获取股票数据
    """
    pro = ts.pro_api('2d1c32104f5167836cfa9d9a3389dd06cc530ef2705e8a17c011b42a')
    data = pro.daily(ts_code=ts_code, start_date=start_date, end_date=end_date)
    return data

def prepare_data(data):
    """
    准备数据集用于分析
    """
    # data['Date'] = pd.to_datetime(data.index)
    data['trade_date'] = pd.to_datetime(data['trade_date'])
    data = data.set_index('trade_date')
    # data['High'].fillna(method='ffill', inplace=True)
    data['high'].fillna(method='ffill', inplace=True)
    # data['Low'].fillna(method='ffill', inplace=True)
    data['low'].fillna(method='ffill', inplace=True)

    return data


def create_candlestick_chart(data, save_path):
    """
    创建蜡烛图并保存
    """
    mpf.plot(data, type='candle', style='charles', title='Stock Price', ylabel='Price', savefig=save_path)


def load_model(model_path):
    """
    加载YOLO模型并设置参数
    """
    model = YOLO(model_path)
    model.overrides['conf'] = 0.25  # 设置置信度阈值
    model.overrides['iou'] = 0.45  # 设置 IoU 阈值
    model.overrides['agnostic_nms'] = False  # NMS 类无关设置
    model.overrides['max_det'] = 1  # 每帧最大检测数
    return model


def predict_trends(model, chart_path):
    """
    读取图像并预测"涨"和"跌"
    """
    image = cv2.imread(chart_path)
    results = model.predict(image, verbose=False)
    render = render_result(model=model, image=image, result=results[0])
    return render


def main():
    """
    主函数：获取数据，准备数据，生成预测并保存结果
    """
    ts_code = '000008.SZ'  # 股票代码
    start_date = '20231001'  # 开始日期
    end_date = '20240412' # 结束日期
    model_path = 'yolov8n.pt'  # 模型路径
    chart_save_path = 'candlestick_chart.png'  # 图表保存路径

    # 获取和准备数据
    data = fetch_stock_data(ts_code, start_date, end_date)
    data = prepare_data(data)

    if data.empty:
        print(f"给定日期范围内未找到数据：{start_date} 至 {end_date}")
        return

    # 创建蜡烛图
    create_candlestick_chart(data, chart_save_path)

    # 加载模型并预测
    model = load_model(model_path)
    render = predict_trends(model, chart_save_path)
    render.save('prediction_result.png')

    print(f"预测结果已保存为 prediction_result.png")


if __name__ == "__main__":

    # ts_code = '000008.SZ'
    # start_date='20231001'
    # end_date='20240412'
    # pro = ts.pro_api('2d1c32104f5167836cfa9d9a3389dd06cc530ef2705e8a17c011b42a')
    # df = pro.daily(ts_code=ts_code, start_date=start_date, end_date=end_date)
    # print(df)

    model_path = 'yolov8n.pt'  # 模型路径
    model = YOLO(model_path)

    # main()
